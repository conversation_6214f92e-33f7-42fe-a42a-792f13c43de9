<svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
  <!-- Dice cube faces -->
  <defs>
    <linearGradient id="topFace" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#e0e0e0;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="leftFace" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#d0d0d0;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#a0a0a0;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="rightFace" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#c0c0c0;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#909090;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Top face -->
  <polygon points="20,30 50,15 80,30 50,45" fill="url(#topFace)" stroke="#333" stroke-width="1"/>
  
  <!-- Left face -->
  <polygon points="20,30 50,45 50,75 20,60" fill="url(#leftFace)" stroke="#333" stroke-width="1"/>
  
  <!-- Right face -->
  <polygon points="50,45 80,30 80,60 50,75" fill="url(#rightFace)" stroke="#333" stroke-width="1"/>
  
  <!-- Dots on top face (showing 5) -->
  <circle cx="30" cy="25" r="2" fill="#333"/>
  <circle cx="70" cy="35" r="2" fill="#333"/>
  <circle cx="30" cy="35" r="2" fill="#333"/>
  <circle cx="70" cy="25" r="2" fill="#333"/>
  <circle cx="50" cy="30" r="2" fill="#333"/>
  
  <!-- Dots on left face (showing 2) -->
  <circle cx="30" cy="40" r="1.5" fill="#333"/>
  <circle cx="40" cy="55" r="1.5" fill="#333"/>
  
  <!-- Dots on right face (showing 3) -->
  <circle cx="60" cy="40" r="1.5" fill="#333"/>
  <circle cx="65" cy="50" r="1.5" fill="#333"/>
  <circle cx="70" cy="55" r="1.5" fill="#333"/>
</svg>
