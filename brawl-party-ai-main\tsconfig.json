{"compilerOptions": {"target": "ESNext", "lib": ["dom", "dom.iterable", "esnext"], "types": ["vite/client", "vite-plugin-svgr/client", "vitest/globals"], "jsx": "react-jsx", "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "noImplicitAny": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "module": "ESNext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "removeComments": true, "noUnusedLocals": true}, "ts-node": {"compilerOptions": {"module": "CommonJS"}}, "include": ["**/*.ts", "**/*.tsx", "**/*.mts"]}