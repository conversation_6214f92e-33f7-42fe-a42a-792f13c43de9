{"name": "brawl-party-board-game", "version": "0.1.0", "private": true, "license": "MIT", "engines": {"node": ">=20 <21", "npm": ">=10"}, "scripts": {"start": "concurrently -c cyan,magenta npm:server npm:client", "start:prod": "npm run server:prod", "build": "tsc && vite build", "client": "vite", "server": "ts-node-dev --no-notify ./server/index.ts", "server:prod": "ts-node -T ./server/index.ts", "lint": "eslint . --ext .ts,.tsx", "lint:fix": "eslint . --ext .ts,.tsx --fix", "prettier": "prettier --write", "tsc": "tsc --noEmit --watch", "test": "vitest", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "dependencies": {"@boardgame.io/storage-cache": "^0.6.0", "@react-three/drei": "^9.57.3", "@react-three/fiber": "^8.15.16", "@reduxjs/toolkit": "^2.1.0", "@tanstack/react-table": "^8.11.8", "@tippyjs/react": "^4.2.6", "bgio-postgres": "^1.0.15", "boardgame.io": "^0.50.2", "classnames": "^2.5.1", "dotenv": "^16.4.1", "koa-static": "^5.0.0", "koa2-connect-history-api-fallback": "^0.1.3", "react": "^18.2.0", "react-dom": "^18.2.0", "react-redux": "^9.1.0", "react-router-dom": "^6.22.0", "sass": "^1.70.0", "three": "^0.161.0", "tippy.js": "^6.3.7", "ts-node": "^10.9.2"}, "devDependencies": {"@storybook/addon-essentials": "^7.6.13", "@storybook/addon-interactions": "^7.6.13", "@storybook/addon-links": "^7.6.13", "@storybook/react": "^7.6.13", "@storybook/react-vite": "^7.6.13", "@testing-library/dom": "^9.3.4", "@testing-library/jest-dom": "^6.4.2", "@testing-library/react": "^14.2.1", "@testing-library/user-event": "^14.5.2", "@types/koa-static": "^4.0.4", "@types/node": "^20.11.17", "@types/react": "^18.2.55", "@types/react-dom": "^18.2.19", "@types/react-router-dom": "^5.3.3", "@types/react-table": "^7.7.19", "@types/three": "^0.161.2", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "@vitejs/plugin-react": "^4.2.1", "concurrently": "^8.2.2", "eslint-config-airbnb": "^19.0.4", "eslint-config-airbnb-typescript": "^17.1.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jsx-a11y": "^6.8.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "normalize.css": "^8.0.1", "prettier": "^3.2.5", "storybook": "^7.6.13", "ts-node-dev": "^2.0.0", "typescript": "^5.3.3", "vite": "^5.2.14", "vite-plugin-svgr": "^4.2.0", "vite-tsconfig-paths": "^4.3.1", "vitest": "^1.2.2"}}