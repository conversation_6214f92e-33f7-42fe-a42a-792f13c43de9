<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🎲</text></svg>" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <meta name="description" content="🎲 Brawl Party AI - 3D Multiplayer Board Game!" />
    <link rel="apple-touch-icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🎲</text></svg>" />
    <link
      href="https://fonts.googleapis.com/css2?family=Rubik:wght@400;500;700&display=swap"
      rel="stylesheet"
    />
    <!--
      manifest.json provides metadata used when your web app is installed on a
      user's mobile device or desktop. See https://developers.google.com/web/fundamentals/web-app-manifest/
    -->
    <link rel="manifest" href="/manifest.json" />
    <title>🎲 Brawl Party AI</title>

    <meta property="og:title" content="🎲 Brawl Party AI - 3D Board Game" />
    <meta
      property="og:description"
      content="3D multiplayer board game with dice rolling, health system, and treasure hunting!"
    />
    <meta property="og:image" content="/social_image.png" />

    <meta name="twitter:title" content="🎲 Brawl Party AI - 3D Board Game" />
    <meta
      name="twitter:description"
      content="3D multiplayer board game with dice rolling, health system, and treasure hunting!"
    />
    <meta name="twitter:image" content="/social_image.png" />
    <meta name="twitter:card" content="summary_large_image" />
  </head>

  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
    <script type="module" src="/src/index.tsx"></script>
  </body>
</html>
