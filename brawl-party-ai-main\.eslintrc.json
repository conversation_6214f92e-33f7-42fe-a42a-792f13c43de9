{"root": true, "settings": {"react": {"version": "detect"}}, "parser": "@typescript-eslint/parser", "parserOptions": {"project": ["./tsconfig.json"]}, "plugins": ["@typescript-eslint", "import", "jsx-a11y", "react", "react-hooks"], "extends": ["airbnb", "airbnb-typescript", "eslint:recommended", "plugin:@typescript-eslint/recommended", "plugin:jsx-a11y/recommended", "plugin:import/recommended", "plugin:import/typescript", "plugin:react/recommended", "plugin:react-hooks/recommended", "prettier"], "rules": {"no-plusplus": ["error", {"allowForLoopAfterthoughts": true}], "no-param-reassign": "off", "import/no-extraneous-dependencies": ["error", {"devDependencies": ["vite.config.mts", "**/*.test.*"]}], "import/prefer-default-export": "off", "import/no-unresolved": ["error", {"ignore": ["\\.svg\\?react$"]}], "@typescript-eslint/no-use-before-define": "off", "@typescript-eslint/no-unused-vars": "off", "react/react-in-jsx-scope": "off", "react/jsx-props-no-spreading": "off", "react/require-default-props": "off", "react/no-unknown-property": "off", "react/function-component-definition": ["error", {"namedComponents": "arrow-function"}], "max-len": ["error", {"code": 80, "ignoreUrls": true, "ignorePattern": "^import\\s.+\\sfrom\\s.+;$||/eslint-disable-next-line/"}], "no-mixed-operators": "error", "no-tabs": ["error", {"allowIndentationTabs": true}], "quotes": ["error", "single", {"avoidEscape": true, "allowTemplateLiterals": false}]}}