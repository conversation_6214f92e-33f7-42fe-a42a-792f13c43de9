@import '~styles/shared';

@mixin buttonTheme($color) {
  background: $color;
  @include shadow($color: $color);

  &:hover:not([disabled]) {
    @include shadow($inset: true, $color: $color);
  }
}

.button {
  border-radius: 10px;
  display: flex;
  text-decoration: none;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  border: none;
  padding: 12px 42px;
  text-align: center;
  font-size: 20px;
  font-weight: 600;
  color: $c-white;
  outline: none;
  user-select: none;
  cursor: pointer;

  &--red {
    @include buttonTheme($c-red);
  }

  &--yellow {
    @include buttonTheme($c-light-yellow);
  }

  &--blue {
    @include buttonTheme($c-ocean-blue);
  }

  &--green {
    @include buttonTheme($c-green);
  }

  &--size-small {
    padding: 12px 24px;
    font-size: 14px;
  }

  &:not([disabled]):hover {
    transform: translate(2px, 2px);
  }

  &[disabled] {
    opacity: 0.3;
    cursor: initial;
  }
}
