name: CD

on:
  push:
    branches: [main]

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  pre-cd:
    uses: ./.github/workflows/build.yml

  deploy:
    needs: pre-cd
    runs-on: ubuntu-latest

    steps:
      - name: Deploy to Ren<PERSON>
        uses: fjogeleit/http-request-action@v1
        with:
          url: ${{ secrets.RENDER_DEPLOY_HOOK }}
          method: 'GET'
