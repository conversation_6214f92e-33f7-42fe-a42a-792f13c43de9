name: CI

on:
  pull_request:
    branches: [main]

jobs:
  pre-ci:
    uses: ./.github/workflows/build.yml

  automerge-dependabot:
    needs: pre-ci
    runs-on: ubuntu-latest

    permissions:
      pull-requests: write
      contents: write

    steps:
      - name: Automerge Dependabot
        uses: fastify/github-action-merge-dependabot@v3.0.0
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
