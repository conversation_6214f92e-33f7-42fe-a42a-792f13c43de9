@import '~styles/shared';

$spinner-color: $c-green !default;
$spinner-accent: $c-light-green !default;
$spinner-size: 60px !default;
$speed: 1s;

@mixin display-box($size) {
  width: $size;
  height: $size;
  display: inline-block;
  box-sizing: border-box;
}

.spinner {
  @include display-box($spinner-size);
  position: relative;
}

.loading-page {
  display: flex;
  width: 100%;
  height: 100%;
  align-items: center;
  justify-content: center;
  background-color: $c-blue;
}

.spinner.balls {
  animation: balls-animate $speed linear infinite;

  &::before {
    border-radius: 50%;
    content: ' ';
    @include display-box(calc($spinner-size / 2));
    background-color: $spinner-color;
    position: absolute;
    top: 0;
    left: 0;
    animation: balls-animate-before $speed ease-in-out infinite;
  }

  &::after {
    border-radius: 50%;
    content: ' ';
    @include display-box(calc($spinner-size / 2));
    background-color: $spinner-accent;
    position: absolute;
    bottom: 0;
    right: 0;
    animation: balls-animate-after $speed ease-in-out infinite;
  }
}

@keyframes balls-animate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes balls-animate-before {
  0% {
    transform: translate(-5px, -5px);
  }
  50% {
    transform: translate(0px, 0px);
  }
  100% {
    transform: translate(-5px, -5px);
  }
}

@keyframes balls-animate-after {
  0% {
    transform: translate(5px, 5px);
  }
  50% {
    transform: translate(0px, 0px);
  }
  100% {
    transform: translate(5px, 5px);
  }
}
